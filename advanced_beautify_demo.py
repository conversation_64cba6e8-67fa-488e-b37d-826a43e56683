#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
高级PPT美化演示 - Advanced PPT Beautification Demo
功能：展示多种主题的美化效果和高级功能
"""

import os
import time
from ppt_beautifier import PPTBeautifier
from ppt_themes import ThemeLibrary, BrandTheme, ThemeType
import logging

# 配置日志
logging.basicConfig(level=logging.INFO, format='%(asctime)s - %(levelname)s - %(message)s')
logger = logging.getLogger(__name__)

def beautify_with_all_themes(input_file: str):
    """使用所有主题美化PPT - Beautify PPT with all themes"""
    if not os.path.exists(input_file):
        print(f"❌ 找不到输入文件: {input_file}")
        return
    
    print(f"🎨 开始使用多种主题美化PPT: {input_file}")
    print("=" * 60)
    
    themes = ThemeLibrary.get_all_themes()
    results = []
    
    for theme_name, theme in themes.items():
        try:
            print(f"\n🎯 正在应用主题: {theme.name} ({theme_name.upper()})")
            
            # 创建美化器
            beautifier = PPTBeautifier(theme)
            
            # 生成输出文件名
            name, ext = os.path.splitext(input_file)
            output_file = f"{name}_{theme_name}{ext}"
            
            # 开始美化
            start_time = time.time()
            result = beautifier.beautify_presentation(input_file, output_file)
            end_time = time.time()
            
            processing_time = end_time - start_time
            file_size = os.path.getsize(result) / (1024 * 1024)  # MB
            
            results.append({
                'theme': theme.name,
                'file': result,
                'time': processing_time,
                'size': file_size
            })
            
            print(f"   ✅ 完成: {result}")
            print(f"   ⏱️  处理时间: {processing_time:.2f}秒")
            print(f"   📁 文件大小: {file_size:.2f}MB")
            
        except Exception as e:
            print(f"   ❌ 失败: {e}")
            logger.error(f"主题 {theme_name} 美化失败: {e}")
    
    # 显示汇总结果
    print("\n" + "=" * 60)
    print("📊 美化结果汇总:")
    print("-" * 60)
    
    for result in results:
        print(f"🎨 {result['theme']:<12} | ⏱️  {result['time']:.2f}s | 📁 {result['size']:.2f}MB")
    
    print(f"\n🎉 总共生成了 {len(results)} 个美化版本！")
    return results

def create_custom_brand_theme():
    """创建自定义品牌主题 - Create custom brand theme"""
    print("\n🎨 创建自定义品牌主题演示...")
    
    # 璟盛科技品牌主题
    jingsheng_theme = BrandTheme(
        name="璟盛科技品牌版",
        primary_color=(0, 102, 204),        # 璟盛蓝
        secondary_color=(51, 153, 102),     # 璟盛绿  
        accent_color=(255, 102, 0),         # 活力橙
        text_color=(51, 51, 51),            # 深灰
        background_color=(248, 249, 250),   # 浅灰白
        title_font="Microsoft YaHei",
        body_font="Microsoft YaHei",
        title_size=30,
        body_size=16,
        theme_type=ThemeType.CORPORATE
    )
    
    print(f"✨ 自定义主题: {jingsheng_theme.name}")
    print(f"   主色调: RGB{jingsheng_theme.primary_color}")
    print(f"   辅助色: RGB{jingsheng_theme.secondary_color}")
    print(f"   强调色: RGB{jingsheng_theme.accent_color}")
    
    return jingsheng_theme

def batch_beautify_directory(directory: str = "."):
    """批量美化目录中的PPT文件 - Batch beautify PPT files in directory"""
    print(f"\n📁 批量处理目录: {directory}")
    
    ppt_files = [f for f in os.listdir(directory) 
                 if f.endswith(('.pptx', '.ppt')) and not f.endswith('_beautified.pptx')]
    
    if not ppt_files:
        print("❌ 未找到PPT文件")
        return
    
    print(f"📋 找到 {len(ppt_files)} 个PPT文件:")
    for i, file in enumerate(ppt_files, 1):
        print(f"   {i}. {file}")
    
    # 使用现代主题批量处理
    theme = ThemeLibrary.get_theme_by_name("modern")
    beautifier = PPTBeautifier(theme)
    
    success_count = 0
    for file in ppt_files:
        try:
            print(f"\n🎯 处理: {file}")
            output = beautifier.beautify_presentation(file)
            print(f"   ✅ 完成: {output}")
            success_count += 1
        except Exception as e:
            print(f"   ❌ 失败: {e}")
            logger.error(f"批量处理 {file} 失败: {e}")
    
    print(f"\n📊 批量处理完成: {success_count}/{len(ppt_files)} 个文件成功")

def analyze_ppt_structure(file_path: str):
    """分析PPT结构 - Analyze PPT structure"""
    try:
        from pptx import Presentation
        
        print(f"\n🔍 分析PPT结构: {file_path}")
        prs = Presentation(file_path)
        
        print(f"📄 总页数: {len(prs.slides)}")
        print(f"📐 页面尺寸: {prs.slide_width} x {prs.slide_height}")
        
        # 分析每页内容
        shape_types = {}
        total_shapes = 0
        
        for i, slide in enumerate(prs.slides, 1):
            slide_shapes = len(slide.shapes)
            total_shapes += slide_shapes
            
            print(f"   第{i}页: {slide_shapes}个元素")
            
            for shape in slide.shapes:
                shape_type = str(shape.shape_type)
                shape_types[shape_type] = shape_types.get(shape_type, 0) + 1
        
        print(f"\n📊 元素统计:")
        print(f"   总元素数: {total_shapes}")
        for shape_type, count in sorted(shape_types.items()):
            print(f"   {shape_type}: {count}个")
            
    except Exception as e:
        print(f"❌ 分析失败: {e}")

def performance_test():
    """性能测试 - Performance test"""
    print("\n⚡ 性能测试...")
    
    input_file = "成都璟盛信息科技有限公司介绍.pptx"
    if not os.path.exists(input_file):
        print("❌ 测试文件不存在")
        return
    
    # 测试不同主题的处理速度
    themes_to_test = ["corporate", "modern", "minimal"]
    
    print("🏃‍♂️ 测试处理速度...")
    for theme_name in themes_to_test:
        theme = ThemeLibrary.get_theme_by_name(theme_name)
        beautifier = PPTBeautifier(theme)
        
        start_time = time.time()
        output = beautifier.beautify_presentation(
            input_file, 
            f"test_{theme_name}.pptx"
        )
        end_time = time.time()
        
        processing_time = end_time - start_time
        print(f"   {theme.name}: {processing_time:.2f}秒")
        
        # 清理测试文件
        if os.path.exists(output):
            os.remove(output)

def main():
    """主函数 - Main function"""
    print("🚀 高级PPT美化演示程序")
    print("=" * 60)
    
    input_file = "成都璟盛信息科技有限公司介绍.pptx"
    
    if not os.path.exists(input_file):
        print(f"❌ 找不到演示文件: {input_file}")
        print("💡 请确保PPT文件存在于当前目录")
        return
    
    try:
        # 1. 分析PPT结构
        analyze_ppt_structure(input_file)
        
        # 2. 创建自定义主题
        custom_theme = create_custom_brand_theme()
        
        # 3. 使用自定义主题美化
        print(f"\n🎯 使用自定义主题美化...")
        beautifier = PPTBeautifier(custom_theme)
        custom_output = beautifier.beautify_presentation(
            input_file, 
            "成都璟盛信息科技有限公司介绍_custom.pptx"
        )
        print(f"✅ 自定义主题版本: {custom_output}")
        
        # 4. 多主题美化（可选，会生成多个文件）
        choice = input("\n❓ 是否生成所有主题版本？(y/N): ").strip().lower()
        if choice == 'y':
            beautify_with_all_themes(input_file)
        
        # 5. 性能测试
        performance_test()
        
        print("\n🎉 演示完成！")
        print("📁 查看生成的文件了解不同主题效果")
        
    except Exception as e:
        logger.error(f"演示程序执行失败: {e}")
        print(f"❌ 执行失败: {e}")

if __name__ == "__main__":
    main()
