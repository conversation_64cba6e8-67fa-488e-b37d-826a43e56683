# 🎨 PPT美化工具 - PowerPoint Beautifier

> 专业的PPT自动化美化解决方案，让你的演示文稿更加专业和美观

## 📋 功能特性

### ✨ 核心功能
- **🎯 一键美化**：自动应用专业主题和样式
- **🎨 多种主题**：6种预设主题（企业、现代、极简、创意、科技、学术）
- **📊 智能布局**：自动优化文本、图表、表格布局
- **🌈 颜色管理**：统一配色方案，品牌色彩应用
- **📝 字体优化**：标准化字体族，层次化排版
- **📈 图表美化**：数据可视化样式统一

### 🛠️ 技术特性
- **🔧 模块化设计**：遵循SOLID原则，易于扩展
- **📊 异常处理**：完善的错误处理和日志记录
- **⚡ 高性能**：批量处理，快速美化
- **🔍 智能识别**：自动识别标题、正文、图表等元素

## 🚀 快速开始

### 1. 环境配置
```bash
# 运行环境配置脚本
python setup_ppt_tools.py
```

### 2. 基础使用
```python
from ppt_beautifier import PPTBeautifier
from ppt_themes import ThemeLibrary

# 创建美化器
beautifier = PPTBeautifier()

# 美化PPT文件
output_file = beautifier.beautify_presentation("input.pptx")
print(f"美化完成: {output_file}")
```

### 3. 自定义主题
```python
from ppt_themes import ThemeLibrary

# 使用预设主题
theme = ThemeLibrary.get_theme_by_name("modern")
beautifier = PPTBeautifier(theme)

# 查看所有可用主题
themes = ThemeLibrary.get_all_themes()
for name, theme in themes.items():
    print(f"{name}: {theme.name}")
```

## 🎨 主题展示

### 📊 预设主题列表

| 主题名称 | 风格特点 | 适用场景 | 主色调 |
|---------|---------|---------|--------|
| **Corporate** | 专业稳重 | 企业汇报、商务演示 | 专业蓝 #0066CC |
| **Modern** | 时尚现代 | 产品发布、创新展示 | 现代紫 #4051B5 |
| **Minimal** | 极简清爽 | 设计展示、简约风格 | 深黑 #212121 |
| **Creative** | 活力创意 | 创意提案、头脑风暴 | 活力粉 #E91E63 |
| **Tech** | 科技未来 | 技术分享、产品介绍 | 科技蓝 #00BCD4 |
| **Academic** | 学术严谨 | 学术报告、研究展示 | 学术蓝 #3F51B5 |

### 🌈 颜色系统
- **主色调**：品牌主色，用于标题和重点强调
- **辅助色**：配合主色，用于次要元素
- **强调色**：突出重点，用于按钮和链接
- **文本色**：正文内容，确保可读性
- **背景色**：页面背景，营造整体氛围

## 📁 项目结构

```
ppt/
├── ppt_beautifier.py      # 核心美化引擎
├── ppt_themes.py          # 主题配置库
├── setup_ppt_tools.py     # 环境配置脚本
├── requirements.txt       # 依赖包列表
├── README.md             # 使用说明
├── ppt_beautifier.log    # 运行日志
└── 成都璟盛信息科技有限公司介绍.pptx  # 示例PPT文件
```

## 🔧 高级配置

### 自定义主题创建
```python
from ppt_themes import BrandTheme, ThemeType

# 创建自定义主题
custom_theme = BrandTheme(
    name="我的品牌主题",
    primary_color=(255, 0, 0),      # 红色主调
    secondary_color=(128, 128, 128), # 灰色辅助
    accent_color=(0, 255, 0),       # 绿色强调
    text_color=(51, 51, 51),        # 深灰文本
    background_color=(255, 255, 255), # 白色背景
    title_font="Microsoft YaHei",
    body_font="Microsoft YaHei",
    title_size=32,
    body_size=18,
    theme_type=ThemeType.CORPORATE
)

# 使用自定义主题
beautifier = PPTBeautifier(custom_theme)
```

### 批量处理
```python
import os
from ppt_beautifier import PPTBeautifier

beautifier = PPTBeautifier()

# 批量处理目录中的所有PPT文件
for filename in os.listdir("."):
    if filename.endswith((".pptx", ".ppt")):
        try:
            output = beautifier.beautify_presentation(filename)
            print(f"✅ {filename} -> {output}")
        except Exception as e:
            print(f"❌ {filename} 处理失败: {e}")
```

## 📊 功能详解

### 🎯 智能布局优化
- **标题居中**：自动识别并居中对齐标题
- **间距调整**：优化元素间距，提升视觉效果
- **对齐统一**：确保页面元素对齐一致

### 🎨 颜色主题应用
- **文本颜色**：根据主题自动设置文本颜色
- **背景填充**：表格、形状背景色统一
- **图表配色**：数据图表颜色协调

### 📝 字体标准化
- **字体族统一**：标题和正文字体规范化
- **字号层次**：建立清晰的字号层次结构
- **样式一致**：粗体、斜体等样式统一

## 🔍 故障排除

### 常见问题

**Q: 安装依赖包失败？**
```bash
# 尝试升级pip
python -m pip install --upgrade pip

# 使用国内镜像源
pip install -r requirements.txt -i https://pypi.tuna.tsinghua.edu.cn/simple/
```

**Q: PPT文件打不开？**
- 确保文件格式为 `.pptx`（不支持旧版 `.ppt`）
- 检查文件是否损坏或被占用
- 确认文件路径正确

**Q: 美化效果不明显？**
- 检查原PPT是否已有复杂样式
- 尝试不同主题看效果差异
- 查看日志文件了解处理详情

### 日志分析
```bash
# 查看详细日志
tail -f ppt_beautifier.log

# 搜索错误信息
grep "ERROR" ppt_beautifier.log
```

## 🚧 开发计划

### 🎯 近期目标
- [ ] 支持更多图表类型美化
- [ ] 添加动画效果配置
- [ ] 实现模板导入功能
- [ ] 支持批量水印添加

### 🔮 长期规划
- [ ] Web界面开发
- [ ] AI智能布局建议
- [ ] 云端主题库
- [ ] 多语言支持

## 📞 技术支持

- **日志文件**：`ppt_beautifier.log`
- **问题反馈**：查看控制台输出和日志文件
- **性能监控**：关注处理时间和内存使用

## 📄 许可证

本项目遵循MIT许可证，详见LICENSE文件。

---

**🎉 让每一份PPT都成为艺术品！**
