#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
PPT美化工具 - PowerPoint Beautifier
功能：自动化PPT样式优化和美化处理
作者：安全开发工程师
"""

from pptx import Presentation
from pptx.util import Inches, Pt
from pptx.dml.color import RGBColor
from pptx.enum.text import PP_ALIGN, MSO_ANCHOR
from pptx.enum.shapes import MSO_SHAPE_TYPE
import os
import logging
from typing import Dict, List, Tuple, Optional
from dataclasses import dataclass

# 配置日志 - 全局日志控制
logging.basicConfig(
    level=logging.INFO,
    format='%(asctime)s - %(levelname)s - %(message)s',
    handlers=[
        logging.FileHandler('ppt_beautifier.log', encoding='utf-8'),
        logging.StreamHandler()
    ]
)
logger = logging.getLogger(__name__)

@dataclass
class BrandTheme:
    """品牌主题配置 - Brand Theme Configuration"""
    primary_color: Tuple[int, int, int] = (0, 102, 204)      # 主色调 - Primary Blue
    secondary_color: Tuple[int, int, int] = (255, 102, 0)    # 辅助色 - Orange
    accent_color: Tuple[int, int, int] = (51, 153, 102)      # 强调色 - Green
    text_color: Tuple[int, int, int] = (51, 51, 51)          # 文本色 - Dark Gray
    background_color: Tuple[int, int, int] = (248, 249, 250) # 背景色 - Light Gray
    
    title_font: str = "Microsoft YaHei"      # 标题字体
    body_font: str = "Microsoft YaHei"       # 正文字体
    title_size: int = 28                     # 标题字号
    body_size: int = 16                      # 正文字号

class PPTBeautifier:
    """PPT美化器 - PowerPoint Beautifier"""
    
    def __init__(self, theme: BrandTheme = None):
        """初始化美化器 - Initialize Beautifier"""
        self.theme = theme or BrandTheme()
        logger.info("PPT美化器初始化完成 - PPT Beautifier initialized")
    
    def load_presentation(self, file_path: str) -> Presentation:
        """加载PPT文件 - Load PowerPoint file"""
        try:
            if not os.path.exists(file_path):
                raise FileNotFoundError(f"PPT文件不存在: {file_path}")
            
            prs = Presentation(file_path)
            logger.info(f"成功加载PPT文件: {file_path}, 共{len(prs.slides)}页")
            return prs
            
        except Exception as e:
            logger.error(f"加载PPT文件失败: {e}")
            raise
    
    def apply_theme_colors(self, prs: Presentation) -> None:
        """应用主题颜色 - Apply theme colors"""
        try:
            logger.info("开始应用主题颜色...")
            
            for slide_idx, slide in enumerate(prs.slides, 1):
                logger.debug(f"处理第{slide_idx}页幻灯片颜色")
                
                for shape in slide.shapes:
                    self._apply_shape_colors(shape)
                    
            logger.info("主题颜色应用完成")
            
        except Exception as e:
            logger.error(f"应用主题颜色失败: {e}")
            raise
    
    def _apply_shape_colors(self, shape) -> None:
        """应用形状颜色 - Apply colors to shape"""
        try:
            # 处理文本框 - Handle text frames
            if hasattr(shape, 'text_frame') and shape.text_frame:
                self._style_text_frame(shape.text_frame)
            
            # 处理表格 - Handle tables
            if shape.shape_type == MSO_SHAPE_TYPE.TABLE:
                self._style_table(shape.table)
                
            # 处理图表 - Handle charts
            if hasattr(shape, 'chart') and shape.chart:
                self._style_chart(shape.chart)
                
        except Exception as e:
            logger.warning(f"形状颜色应用警告: {e}")
    
    def _style_text_frame(self, text_frame) -> None:
        """样式化文本框 - Style text frame"""
        try:
            for paragraph in text_frame.paragraphs:
                for run in paragraph.runs:
                    # 设置字体 - Set font
                    run.font.name = self.theme.body_font
                    run.font.size = Pt(self.theme.body_size)
                    run.font.color.rgb = RGBColor(*self.theme.text_color)
                    
                # 设置段落对齐 - Set paragraph alignment
                if paragraph.level == 0:  # 标题级别
                    paragraph.font.name = self.theme.title_font
                    paragraph.font.size = Pt(self.theme.title_size)
                    paragraph.font.color.rgb = RGBColor(*self.theme.primary_color)
                    
        except Exception as e:
            logger.warning(f"文本框样式化警告: {e}")
    
    def _style_table(self, table) -> None:
        """样式化表格 - Style table"""
        try:
            # 表头样式 - Header style
            for cell in table.rows[0].cells:
                cell.fill.solid()
                cell.fill.fore_color.rgb = RGBColor(*self.theme.primary_color)
                
                # 表头文字 - Header text
                if cell.text_frame:
                    for paragraph in cell.text_frame.paragraphs:
                        for run in paragraph.runs:
                            run.font.color.rgb = RGBColor(255, 255, 255)  # 白色文字
                            run.font.bold = True
                            
        except Exception as e:
            logger.warning(f"表格样式化警告: {e}")
    
    def _style_chart(self, chart) -> None:
        """样式化图表 - Style chart"""
        try:
            # 这里可以添加图表美化逻辑
            # Chart beautification logic can be added here
            logger.debug("图表样式化处理")
            
        except Exception as e:
            logger.warning(f"图表样式化警告: {e}")
    
    def optimize_layout(self, prs: Presentation) -> None:
        """优化布局 - Optimize layout"""
        try:
            logger.info("开始优化布局...")
            
            for slide_idx, slide in enumerate(prs.slides, 1):
                logger.debug(f"优化第{slide_idx}页布局")
                self._optimize_slide_layout(slide)
                
            logger.info("布局优化完成")
            
        except Exception as e:
            logger.error(f"布局优化失败: {e}")
            raise
    
    def _optimize_slide_layout(self, slide) -> None:
        """优化单页布局 - Optimize single slide layout"""
        try:
            # 标题居中对齐 - Center align titles
            for shape in slide.shapes:
                if hasattr(shape, 'text_frame') and shape.text_frame:
                    # 检查是否为标题 - Check if it's a title
                    if self._is_title_shape(shape):
                        for paragraph in shape.text_frame.paragraphs:
                            paragraph.alignment = PP_ALIGN.CENTER
                            
        except Exception as e:
            logger.warning(f"单页布局优化警告: {e}")
    
    def _is_title_shape(self, shape) -> bool:
        """判断是否为标题形状 - Check if shape is title"""
        try:
            # 简单的标题判断逻辑 - Simple title detection logic
            if hasattr(shape, 'text_frame') and shape.text_frame.text:
                text = shape.text_frame.text.strip()
                # 标题通常较短且位于页面上方 - Titles are usually short and at top
                return len(text) < 50 and shape.top < Inches(2)
            return False
            
        except Exception as e:
            logger.warning(f"标题判断警告: {e}")
            return False
    
    def beautify_presentation(self, input_path: str, output_path: str = None) -> str:
        """美化PPT - Beautify PowerPoint presentation"""
        try:
            logger.info(f"开始美化PPT: {input_path}")
            
            # 加载PPT - Load presentation
            prs = self.load_presentation(input_path)
            
            # 应用主题 - Apply theme
            self.apply_theme_colors(prs)
            
            # 优化布局 - Optimize layout
            self.optimize_layout(prs)
            
            # 保存文件 - Save file
            if not output_path:
                name, ext = os.path.splitext(input_path)
                output_path = f"{name}_beautified{ext}"
            
            prs.save(output_path)
            logger.info(f"PPT美化完成，保存至: {output_path}")
            
            return output_path
            
        except Exception as e:
            logger.error(f"PPT美化失败: {e}")
            raise

def main():
    """主函数 - Main function"""
    try:
        # 创建美化器 - Create beautifier
        beautifier = PPTBeautifier()
        
        # 美化PPT - Beautify presentation
        input_file = "成都璟盛信息科技有限公司介绍.pptx"
        if os.path.exists(input_file):
            output_file = beautifier.beautify_presentation(input_file)
            print(f"✅ PPT美化完成: {output_file}")
        else:
            print(f"❌ 找不到PPT文件: {input_file}")
            
    except Exception as e:
        logger.error(f"程序执行失败: {e}")
        print(f"❌ 执行失败: {e}")

if __name__ == "__main__":
    main()
