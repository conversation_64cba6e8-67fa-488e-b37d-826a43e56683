#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
PPT工具环境配置脚本 - PPT Tools Setup Script
功能：自动安装和配置PPT处理所需的依赖库
"""

import subprocess
import sys
import os
import logging

# 配置日志
logging.basicConfig(level=logging.INFO, format='%(asctime)s - %(levelname)s - %(message)s')
logger = logging.getLogger(__name__)

# PPT处理所需的依赖包 - Required packages for PPT processing
REQUIRED_PACKAGES = [
    'python-pptx>=0.6.21',      # 核心PPT处理库 - Core PPT library
    'Pillow>=9.0.0',            # 图像处理 - Image processing
    'matplotlib>=3.5.0',        # 图表生成 - Chart generation
    'seaborn>=0.11.0',          # 数据可视化美化 - Data visualization
    'colorthief>=0.2.1',        # 颜色提取 - Color extraction
    'webcolors>=1.12',          # 颜色处理 - Color handling
    'fonttools>=4.33.0',        # 字体处理 - Font handling
    'openpyxl>=3.0.0',          # Excel处理（数据导入）- Excel processing
]

def check_python_version():
    """检查Python版本 - Check Python version"""
    version = sys.version_info
    logger.info(f"当前Python版本: {version.major}.{version.minor}.{version.micro}")
    
    if version.major < 3 or (version.major == 3 and version.minor < 8):
        logger.error("需要Python 3.8或更高版本")
        return False
    return True

def install_package(package):
    """安装单个包 - Install single package"""
    try:
        logger.info(f"正在安装: {package}")
        result = subprocess.run(
            [sys.executable, '-m', 'pip', 'install', package],
            capture_output=True,
            text=True,
            check=True
        )
        logger.info(f"✅ {package} 安装成功")
        return True
        
    except subprocess.CalledProcessError as e:
        logger.error(f"❌ {package} 安装失败: {e.stderr}")
        return False

def install_all_packages():
    """安装所有依赖包 - Install all required packages"""
    logger.info("开始安装PPT处理依赖包...")
    
    success_count = 0
    failed_packages = []
    
    for package in REQUIRED_PACKAGES:
        if install_package(package):
            success_count += 1
        else:
            failed_packages.append(package)
    
    logger.info(f"安装完成: {success_count}/{len(REQUIRED_PACKAGES)} 个包成功安装")
    
    if failed_packages:
        logger.warning(f"安装失败的包: {failed_packages}")
        return False
    
    return True

def verify_installation():
    """验证安装 - Verify installation"""
    logger.info("验证安装结果...")
    
    test_imports = [
        ('pptx', 'python-pptx'),
        ('PIL', 'Pillow'),
        ('matplotlib', 'matplotlib'),
        ('seaborn', 'seaborn'),
        ('colorthief', 'colorthief'),
        ('webcolors', 'webcolors'),
        ('fontTools', 'fonttools'),
        ('openpyxl', 'openpyxl'),
    ]
    
    success_count = 0
    for module_name, package_name in test_imports:
        try:
            __import__(module_name)
            logger.info(f"✅ {package_name} 导入成功")
            success_count += 1
        except ImportError as e:
            logger.error(f"❌ {package_name} 导入失败: {e}")
    
    logger.info(f"验证完成: {success_count}/{len(test_imports)} 个包可正常使用")
    return success_count == len(test_imports)

def create_requirements_file():
    """创建requirements.txt文件 - Create requirements.txt file"""
    try:
        with open('requirements.txt', 'w', encoding='utf-8') as f:
            f.write("# PPT处理工具依赖包 - PPT Processing Tool Dependencies\n")
            f.write("# 安装命令: pip install -r requirements.txt\n\n")
            for package in REQUIRED_PACKAGES:
                f.write(f"{package}\n")
        
        logger.info("✅ requirements.txt 文件已创建")
        return True
        
    except Exception as e:
        logger.error(f"❌ 创建requirements.txt失败: {e}")
        return False

def main():
    """主函数 - Main function"""
    print("🚀 PPT工具环境配置开始...")
    print("=" * 50)
    
    # 检查Python版本
    if not check_python_version():
        print("❌ Python版本不符合要求，请升级到3.8+")
        return False
    
    # 创建requirements文件
    create_requirements_file()
    
    # 安装依赖包
    if not install_all_packages():
        print("⚠️  部分包安装失败，请检查网络连接或手动安装")
        print("💡 可以尝试: pip install -r requirements.txt")
        return False
    
    # 验证安装
    if not verify_installation():
        print("⚠️  部分包验证失败，可能需要重新安装")
        return False
    
    print("=" * 50)
    print("🎉 PPT工具环境配置完成！")
    print("\n📋 下一步操作:")
    print("1. 运行 python ppt_beautifier.py 开始美化PPT")
    print("2. 查看 ppt_beautifier.log 了解处理日志")
    print("3. 美化后的文件会保存为 *_beautified.pptx")
    
    return True

if __name__ == "__main__":
    try:
        success = main()
        sys.exit(0 if success else 1)
    except KeyboardInterrupt:
        print("\n⏹️  用户中断操作")
        sys.exit(1)
    except Exception as e:
        logger.error(f"程序执行异常: {e}")
        print(f"❌ 执行失败: {e}")
        sys.exit(1)
