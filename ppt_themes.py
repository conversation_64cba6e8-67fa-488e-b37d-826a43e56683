#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
PPT主题配置库 - PPT Theme Configuration Library
功能：提供多种预设主题和自定义主题配置
"""

from dataclasses import dataclass
from typing import Dict, List, Tuple
from enum import Enum

class ThemeType(Enum):
    """主题类型枚举 - Theme type enumeration"""
    CORPORATE = "corporate"      # 企业风格
    MODERN = "modern"           # 现代风格
    MINIMAL = "minimal"         # 极简风格
    CREATIVE = "creative"       # 创意风格
    TECH = "tech"              # 科技风格
    ACADEMIC = "academic"       # 学术风格

@dataclass
class BrandTheme:
    """品牌主题配置 - Brand Theme Configuration"""
    name: str
    primary_color: Tuple[int, int, int]
    secondary_color: Tuple[int, int, int]
    accent_color: Tuple[int, int, int]
    text_color: Tuple[int, int, int]
    background_color: <PERSON>ple[int, int, int]
    title_font: str
    body_font: str
    title_size: int
    body_size: int
    theme_type: ThemeType

class ThemeLibrary:
    """主题库 - Theme Library"""
    
    @staticmethod
    def get_corporate_theme() -> BrandTheme:
        """企业风格主题 - Corporate theme"""
        return BrandTheme(
            name="企业专业版",
            primary_color=(0, 102, 204),        # 专业蓝
            secondary_color=(102, 102, 102),     # 中性灰
            accent_color=(255, 102, 0),          # 活力橙
            text_color=(51, 51, 51),             # 深灰
            background_color=(255, 255, 255),    # 纯白
            title_font="Microsoft YaHei",
            body_font="Microsoft YaHei",
            title_size=32,
            body_size=18,
            theme_type=ThemeType.CORPORATE
        )
    
    @staticmethod
    def get_modern_theme() -> BrandTheme:
        """现代风格主题 - Modern theme"""
        return BrandTheme(
            name="现代简约版",
            primary_color=(64, 81, 181),         # 现代紫
            secondary_color=(156, 39, 176),      # 活力紫
            accent_color=(255, 193, 7),          # 金黄色
            text_color=(33, 33, 33),             # 深黑
            background_color=(250, 250, 250),    # 浅灰白
            title_font="Microsoft YaHei UI",
            body_font="Microsoft YaHei UI",
            title_size=30,
            body_size=16,
            theme_type=ThemeType.MODERN
        )
    
    @staticmethod
    def get_minimal_theme() -> BrandTheme:
        """极简风格主题 - Minimal theme"""
        return BrandTheme(
            name="极简黑白版",
            primary_color=(33, 33, 33),          # 深黑
            secondary_color=(117, 117, 117),     # 中灰
            accent_color=(76, 175, 80),          # 清新绿
            text_color=(66, 66, 66),             # 文本灰
            background_color=(255, 255, 255),    # 纯白
            title_font="Microsoft YaHei Light",
            body_font="Microsoft YaHei Light",
            title_size=28,
            body_size=16,
            theme_type=ThemeType.MINIMAL
        )
    
    @staticmethod
    def get_creative_theme() -> BrandTheme:
        """创意风格主题 - Creative theme"""
        return BrandTheme(
            name="创意活力版",
            primary_color=(233, 30, 99),         # 活力粉
            secondary_color=(156, 39, 176),      # 紫色
            accent_color=(255, 152, 0),          # 橙色
            text_color=(51, 51, 51),             # 深灰
            background_color=(248, 248, 248),    # 浅灰
            title_font="Microsoft YaHei",
            body_font="Microsoft YaHei",
            title_size=34,
            body_size=18,
            theme_type=ThemeType.CREATIVE
        )
    
    @staticmethod
    def get_tech_theme() -> BrandTheme:
        """科技风格主题 - Tech theme"""
        return BrandTheme(
            name="科技未来版",
            primary_color=(0, 188, 212),         # 科技蓝
            secondary_color=(96, 125, 139),      # 蓝灰
            accent_color=(76, 175, 80),          # 科技绿
            text_color=(38, 50, 56),             # 深蓝灰
            background_color=(245, 245, 245),    # 浅灰
            title_font="Microsoft YaHei UI",
            body_font="Microsoft YaHei UI",
            title_size=30,
            body_size=16,
            theme_type=ThemeType.TECH
        )
    
    @staticmethod
    def get_academic_theme() -> BrandTheme:
        """学术风格主题 - Academic theme"""
        return BrandTheme(
            name="学术严谨版",
            primary_color=(63, 81, 181),         # 学术蓝
            secondary_color=(121, 85, 72),       # 棕色
            accent_color=(255, 87, 34),          # 橙红
            text_color=(33, 33, 33),             # 深黑
            background_color=(255, 255, 255),    # 纯白
            title_font="Times New Roman",
            body_font="Microsoft YaHei",
            title_size=28,
            body_size=16,
            theme_type=ThemeType.ACADEMIC
        )
    
    @classmethod
    def get_all_themes(cls) -> Dict[str, BrandTheme]:
        """获取所有预设主题 - Get all preset themes"""
        return {
            "corporate": cls.get_corporate_theme(),
            "modern": cls.get_modern_theme(),
            "minimal": cls.get_minimal_theme(),
            "creative": cls.get_creative_theme(),
            "tech": cls.get_tech_theme(),
            "academic": cls.get_academic_theme(),
        }
    
    @classmethod
    def get_theme_by_name(cls, theme_name: str) -> BrandTheme:
        """根据名称获取主题 - Get theme by name"""
        themes = cls.get_all_themes()
        if theme_name.lower() in themes:
            return themes[theme_name.lower()]
        else:
            # 默认返回企业主题 - Default to corporate theme
            return cls.get_corporate_theme()

class ColorPalette:
    """颜色调色板 - Color Palette"""
    
    # 企业常用色彩 - Common corporate colors
    CORPORATE_BLUES = [
        (0, 102, 204),    # 标准蓝
        (25, 118, 210),   # 亮蓝
        (13, 71, 161),    # 深蓝
        (21, 101, 192),   # 中蓝
    ]
    
    CORPORATE_GRAYS = [
        (97, 97, 97),     # 深灰
        (158, 158, 158),  # 中灰
        (189, 189, 189),  # 浅灰
        (224, 224, 224),  # 极浅灰
    ]
    
    # 现代流行色 - Modern trendy colors
    MODERN_COLORS = [
        (64, 81, 181),    # 靛蓝
        (156, 39, 176),   # 紫色
        (233, 30, 99),    # 粉色
        (244, 67, 54),    # 红色
        (255, 152, 0),    # 橙色
        (255, 193, 7),    # 黄色
        (76, 175, 80),    # 绿色
        (0, 188, 212),    # 青色
    ]
    
    @staticmethod
    def get_complementary_color(rgb: Tuple[int, int, int]) -> Tuple[int, int, int]:
        """获取互补色 - Get complementary color"""
        r, g, b = rgb
        return (255 - r, 255 - g, 255 - b)
    
    @staticmethod
    def lighten_color(rgb: Tuple[int, int, int], factor: float = 0.3) -> Tuple[int, int, int]:
        """调亮颜色 - Lighten color"""
        r, g, b = rgb
        r = min(255, int(r + (255 - r) * factor))
        g = min(255, int(g + (255 - g) * factor))
        b = min(255, int(b + (255 - b) * factor))
        return (r, g, b)
    
    @staticmethod
    def darken_color(rgb: Tuple[int, int, int], factor: float = 0.3) -> Tuple[int, int, int]:
        """调暗颜色 - Darken color"""
        r, g, b = rgb
        r = max(0, int(r * (1 - factor)))
        g = max(0, int(g * (1 - factor)))
        b = max(0, int(b * (1 - factor)))
        return (r, g, b)

def demo_themes():
    """演示所有主题 - Demo all themes"""
    print("🎨 PPT主题库演示 - PPT Theme Library Demo")
    print("=" * 60)
    
    themes = ThemeLibrary.get_all_themes()
    
    for theme_key, theme in themes.items():
        print(f"\n📋 {theme.name} ({theme_key.upper()})")
        print(f"   主色调: RGB{theme.primary_color}")
        print(f"   辅助色: RGB{theme.secondary_color}")
        print(f"   强调色: RGB{theme.accent_color}")
        print(f"   标题字体: {theme.title_font} ({theme.title_size}pt)")
        print(f"   正文字体: {theme.body_font} ({theme.body_size}pt)")
        print(f"   风格类型: {theme.theme_type.value}")

if __name__ == "__main__":
    demo_themes()
